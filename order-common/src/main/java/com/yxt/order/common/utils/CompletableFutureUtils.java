package com.yxt.order.common.utils;

import cn.hutool.core.collection.CollUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.function.Supplier;
import lombok.SneakyThrows;

public class CompletableFutureUtils {

  /**
   * @param supplierFunction 线程执行函数
   * @param actions          待执行列表
   * @param rateLimit        分批数量
   * @param threadPool       线程池
   * @param <T>              待执行列表的类型
   * @param <R>              执行结果类型
   * @return 执行结果
   */
  public static <T, R> List<R> supplyAsync(Function<T, Supplier<R>> supplierFunction, Collection<T> actions, int rateLimit, Executor threadPool) {
    return supplyWithSleepAsync(supplierFunction, actions, -1, rateLimit, threadPool);
  }

  /**
   * @param supplierFunction 线程执行函数
   * @param actions          待执行列表
   * @param sleepMills       每执行完一批任务，等待的毫秒数
   * @param rateLimit        分批数量
   * @param threadPool       线程池
   * @param <T>              待执行列表的类型
   * @param <R>              执行结果类型
   * @return 执行结果
   */
  public static <T, R> List<R> supplyWithSleepAsync(Function<T, Supplier<R>> supplierFunction, Collection<T> actions, long sleepMills, int rateLimit, Executor threadPool) {
    if (CollUtil.isEmpty(actions)) {
      return new ArrayList<>(0);
    }

    // 预分配结果列表大小以提高性能
    List<R> resultList = new ArrayList<>(actions.size());
    List<CompletableFuture<R>> futureList = new ArrayList<>(rateLimit);

    for (T t : actions) {
      // 先添加到批次列表
      CompletableFuture<R> future = CompletableFuture.supplyAsync(supplierFunction.apply(t), threadPool);
      futureList.add(future);

      // 当达到批次大小时，处理当前批次
      if (futureList.size() >= rateLimit) {
        flushFutureList(futureList, resultList);

        // 如果需要休眠，则休眠指定时间
        if (sleepMills > 0) {
          try {
            Thread.sleep(sleepMills);
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Thread was interrupted during sleep", e);
          }
        }
      }
    }

    // 处理剩余的futures
    if (!futureList.isEmpty()) {
      flushFutureList(futureList, resultList);
    }

    return resultList;
  }

  /**
   * @param runnableFunction 线程执行函数
   * @param actions          待执行列表
   * @param rateLimit        分批数量
   * @param threadPool       线程池
   * @param <T>              待执行列表的类型
   */
  public static <T> void runAsync(Function<T, Runnable> runnableFunction, Collection<T> actions, int rateLimit, Executor threadPool) {
    runWithSleepAsync(runnableFunction, actions, -1, rateLimit, threadPool);
  }

  /**
   * @param runnableFunction 线程执行函数
   * @param actions          待执行列表
   * @param sleepMills       每执行完一批任务，等待的毫秒数
   * @param rateLimit        分批数量
   * @param threadPool       线程池
   * @param <T>              待执行列表的类型
   */
  @SneakyThrows
  public static <T> void runWithSleepAsync(Function<T, Runnable> runnableFunction, Collection<T> actions, long sleepMills, int rateLimit, Executor threadPool) {
    if (CollUtil.isEmpty(actions)) {
      return;
    }
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    for (T t : actions) {
      CompletableFuture<Void> future = CompletableFuture.runAsync(runnableFunction.apply(t), threadPool);
      if (futureList.size() >= rateLimit) {
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        if(sleepMills > 0){
          Thread.sleep(sleepMills);
        }
      }
      futureList.add(future);
    }
    if (CollUtil.isNotEmpty(futureList)) {
      CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    }
  }

  private static <R> void flushFutureList(List<CompletableFuture<R>> futureList, Collection<R> resultList) {
    futureList.forEach(temp -> resultList.add(temp.join()));
    futureList.clear();
  }

}
