package com.yxt.order.common.order_world_dto.db;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 心云退单下账单
 */
@Data
public class AccountRefund {

  private Long id;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 退单下账单号
   */
  private String accountRefundNo;

  /**
   * 售后单号
   */
  private String afterSaleNo;

  /**
   * 退货单号
   */
  private String returnNo;

  /**
   * 订单号
   */
  private String orderNo;

  /**
   * HD_H1-海典H1  HD_H2-海典H2  KC-科传
   */
  private String posCode;

  /**
   * POS退单下账单号
   */
  private String posAccountRefundNo;

  /**
   * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
   */
  private String accountRefundStatus;

  /**
   * 退款商品下账总金额=累加明细 bill_total_amount
   */
  private BigDecimal refundBillCommodityAmount;

  /**
   * 退款下账总金额 = bill_commodity_amount+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额
   */
  private BigDecimal refundBillTotalAmount;

  /**
   * 退款商品总金额=累加明细 total_amount
   */
  private BigDecimal refundGoodsTotal;

  /**
   * 商家配送费
   */
  private BigDecimal refundMerchantDeliveryFee;

  /**
   * 商家总优惠=累加明细 discount_share+discount_amount
   */
  private BigDecimal refundMerchantTotalDiscount;

  /**
   * 包装费
   */
  private BigDecimal refundPackageFee;

  /**
   * 退款单接收时间
   */
  private LocalDateTime acceptTime;

  /**
   * 下账时间
   */
  private LocalDateTime billTime;

  /**
   * 退款理由
   */
  private String refundReason;

  /**
   * 订单操作人  O2O-拣货人 id  B2C-发货人id
   */
  private String orderOperatorId;

  /**
   * 会员编号
   */
  private String userId;

  /**
   * 成本中心编码
   */
  private String costCenterCode;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 下账失败原因 下账失败返回
   */
  private String accountErrMsg;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  private Long isValid;

  /**
   * 平台创建日
   */
  private LocalDate createdDay;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
